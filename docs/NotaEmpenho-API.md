# API de Nota de Empenho

Esta documentação descreve os endpoints disponíveis para gerenciar Notas de Empenho no sistema SICAD.

## Endpoints Principais

### 1. Listar Notas de Empenho
```http
GET /notas-empenho
```

**Parâmetros de consulta (opcionais):**
- `numero`: Filtrar por número da nota
- `dataEmissaoInicio`: Data de início para filtro por data de emissão (formato: dd/MM/yyyy)
- `dataEmissaoFim`: Data de fim para filtro por data de emissão (formato: dd/MM/yyyy)
- `valorMinimo`: Valor mínimo para filtro
- `valorMaximo`: Valor máximo para filtro
- `page`: Número da página (padrão: 0)
- `size`: Tam<PERSON><PERSON> da página (padrão: 20)

**Exemplo de resposta:**
```json
{
  "content": [
    {
      "id": 1,
      "numero": "2024001",
      "dataEmissao": "15/08/2024",
      "valorTotal": 1000.00,
      "prazoMaximoEntrega": "15/09/2024",
      "diasNotificacaoPrevia": 5
    }
  ],
  "pageable": {
    "pageNumber": 0,
    "pageSize": 20
  },
  "totalElements": 1
}
```

### 2. Buscar Nota de Empenho por ID
```http
GET /notas-empenho/{id}
```

### 3. Criar Nova Nota de Empenho
```http
POST /notas-empenho
Content-Type: application/json

{
  "numero": "2024001",
  "dataEmissao": "15/08/2024",
  "valorTotal": 1000.00,
  "prazoMaximoEntrega": "15/09/2024",
  "diasNotificacaoPrevia": 5
}
```

### 4. Atualizar Nota de Empenho
```http
PUT /notas-empenho/{id}
Content-Type: application/json

{
  "numero": "2024001",
  "dataEmissao": "15/08/2024",
  "valorTotal": 1500.00,
  "prazoMaximoEntrega": "20/09/2024",
  "diasNotificacaoPrevia": 7
}
```

### 5. Deletar Nota de Empenho
```http
DELETE /notas-empenho/{id}
```

### 6. Anular Empenho Total
```http
POST /notas-empenho/{id}/anular-total
```

### 7. Anular Empenho Parcial
```http
POST /notas-empenho/{id}/anular-parcial?valor=500.00
```

### 8. Buscar por Prazo de Vencimento
```http
GET /notas-empenho/vencimento?dataLimite=31/08/2024
```

## Endpoints Específicos (Getters/Setters)

### Número
```http
GET /notas-empenho/{id}/numero
PUT /notas-empenho/{id}/numero?numero=2024002
```

### Data de Emissão
```http
GET /notas-empenho/{id}/data-emissao
PUT /notas-empenho/{id}/data-emissao?data=16/08/2024
```

### Valor Total
```http
GET /notas-empenho/{id}/valor-total
PUT /notas-empenho/{id}/valor-total?valor=2000.00
```

### Prazo Máximo de Entrega
```http
GET /notas-empenho/{id}/prazo-maximo-entrega
PUT /notas-empenho/{id}/prazo-maximo-entrega?data=30/09/2024
```

### Dias de Notificação Prévia
```http
GET /notas-empenho/{id}/dias-notificacao-previa
PUT /notas-empenho/{id}/dias-notificacao-previa?dias=10
```

## Códigos de Status HTTP

- `200 OK`: Operação realizada com sucesso
- `201 Created`: Recurso criado com sucesso
- `204 No Content`: Recurso deletado com sucesso
- `400 Bad Request`: Dados inválidos na requisição
- `404 Not Found`: Recurso não encontrado
- `409 Conflict`: Conflito (ex: número já existe)

## Validações

- **Número**: Obrigatório, máximo 50 caracteres, deve ser único
- **Data de Emissão**: Obrigatória, formato dd/MM/yyyy
- **Valor Total**: Obrigatório, deve ser positivo
- **Prazo Máximo de Entrega**: Obrigatório, formato dd/MM/yyyy
- **Dias de Notificação Prévia**: Obrigatório, deve ser positivo

## Exemplos de Uso com cURL

### Criar uma nova nota de empenho:
```bash
curl -X POST http://localhost:8080/notas-empenho \
  -H "Content-Type: application/json" \
  -d '{
    "numero": "2024001",
    "dataEmissao": "15/08/2024",
    "valorTotal": 1000.00,
    "prazoMaximoEntrega": "15/09/2024",
    "diasNotificacaoPrevia": 5
  }'
```

### Listar notas com filtros:
```bash
curl "http://localhost:8080/notas-empenho?numero=2024&valorMinimo=500&page=0&size=10"
```

### Anular empenho parcial:
```bash
curl -X POST "http://localhost:8080/notas-empenho/1/anular-parcial?valor=300.00"
```
