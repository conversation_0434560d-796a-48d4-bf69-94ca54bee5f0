package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusSolicitacaoSpal;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "solicitacoes_spal")
public class SolicitacaoSpal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private String numeroSolicitacao;

    @Column(nullable = false)
    private LocalDate dataSolicitacao;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatusSolicitacaoSpal status;

    @Column
    private Integer numeroGlpi;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "solicitante_id", nullable = false)
    private Usuario solicitante;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "processo_id", nullable = false)
    private ProcessoSpal processo;

    // @OneToOne
    // @JoinColumn(name = "documento_permissao_id", unique = true)
    // private Documento documentoPermissao; 
    // Tem que fazer a entidade Documento

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "solicitacao_spal_patrimonios", joinColumns = @JoinColumn(name = "solicitacao_spal_id"))
    @Column(name = "patrimonio", nullable = false)
    private Set<String> patrimonios = new HashSet<>();
}