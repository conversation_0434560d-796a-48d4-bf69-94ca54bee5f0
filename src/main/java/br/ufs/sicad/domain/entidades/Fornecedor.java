package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fornecedor")
@Entity
public class Fornecedor {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "cnpj", nullable = false, unique = true, length = 18)
    private String cnpj;

    @Column(name = "razao_social", nullable = false, length = 255)
    private String razaoSocial;

    @ElementCollection
    @CollectionTable(name = "fornecedor_email", joinColumns = @JoinColumn(name = "fornecedor_id"))
    @Column(name = "email")
    private List<String> emails = new ArrayList<>();

    @ElementCollection
    @CollectionTable(name = "fornecedor_telefone", joinColumns = @JoinColumn(name = "fornecedor_id"))
    @Column(name = "telefone")
    private List<String> telefones = new ArrayList<>();
}