package br.ufs.sicad.domain.entidades;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("CONSUMO") // Valor que será salvo na coluna 'dtype'
public class ProcessoConsumo extends Processo {

    @Column(name = "status_entrega")
    
    private String statusEntrega;
}