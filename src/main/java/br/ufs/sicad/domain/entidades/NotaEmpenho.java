package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "nota_empenho")
public class NotaEmpenho {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "O número é obrigatório")
    @Size(max = 50, message = "O número deve ter no máximo 50 caracteres")
    @Column(name = "numero", nullable = false, unique = true, length = 50)
    private String numero;

    @NotNull(message = "A data de emissão é obrigatória")
    @Column(name = "data_emissao", nullable = false)
    private LocalDate dataEmissao;

    @NotNull(message = "O valor total é obrigatório")
    @Positive(message = "O valor total deve ser positivo")
    @Column(name = "valor_total", nullable = false, precision = 15, scale = 2)
    private BigDecimal valorTotal;

    @NotNull(message = "O prazo máximo de entrega é obrigatório")
    @Column(name = "prazo_maximo_entrega", nullable = false)
    private LocalDate prazoMaximoEntrega;

    @NotNull(message = "Os dias de notificação prévia são obrigatórios")
    @Positive(message = "Os dias de notificação prévia devem ser positivos")
    @Column(name = "dias_notificacao_previa", nullable = false)
    private Integer diasNotificacaoPrevia;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = true)
    private StatusNotaEmpenho status = StatusNotaEmpenho.ATIVA;

    @NotNull(message = "O fornecedor é obrigatório")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fornecedor_id", nullable = false)
    private Fornecedor fornecedor;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contrato_id")
    private Contrato contrato;

    @OneToMany(mappedBy = "notaEmpenho", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private List<Item> itens = new ArrayList<>();

    public NotaEmpenho(String numero, LocalDate dataEmissao, LocalDate prazoMaximoEntrega,
                      Integer diasNotificacaoPrevia, Fornecedor fornecedor) {
        if (fornecedor == null) {
            throw new IllegalArgumentException("Fornecedor é obrigatório para criar uma nota de empenho");
        }
        this.numero = numero;
        this.dataEmissao = dataEmissao;
        this.prazoMaximoEntrega = prazoMaximoEntrega;
        this.diasNotificacaoPrevia = diasNotificacaoPrevia;
        this.fornecedor = fornecedor;
    }

    // Método para anular empenho total
    public void anularEmpenhoTotal() {
    }

    // Método para anular empenho parcial
    public void anularEmpenhoTotal(BigDecimal valor) {
        if (valor != null && valor.compareTo(BigDecimal.ZERO) > 0 && valor.compareTo(this.valorTotal) <= 0) {
            this.valorTotal = this.valorTotal.subtract(valor);
        }
    }

    public void inativar() {
        this.status = StatusNotaEmpenho.INATIVA;
    }

    public void ativar() {
        this.status = StatusNotaEmpenho.ATIVA;
    }

    public boolean isAtiva() {
        return this.status == null || this.status == StatusNotaEmpenho.ATIVA;
    }

    // Método para garantir que o status seja definido
    public StatusNotaEmpenho getStatus() {
        return this.status == null ? StatusNotaEmpenho.ATIVA : this.status;
    }


    public void adicionarItem(Item item) {
        this.itens.add(item);
    }

    public void removerItem(Item item) {
        this.itens.remove(item);
    }

    public void limparItens() {
        this.itens.clear();
    }

//    // Método para calcular valor total baseado nos itens
//    public BigDecimal calcularValorTotalItens() {
//        return itens.stream()
//                .map(NotaEmpenhoItem::getValorTotal)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }

    // Método para validar se tem pelo menos um item
    public boolean temItens() {
        return itens != null && !itens.isEmpty();
    }

    // Método para validar regras de negócio antes de salvar
    public void validarRegrasDeNegocio() {
        if (fornecedor == null) {
            throw new IllegalArgumentException("Nota de empenho deve ter um fornecedor");
        }
        if (!temItens()) {
            throw new IllegalArgumentException("Nota de empenho deve ter pelo menos um item");
        }
    }
}
