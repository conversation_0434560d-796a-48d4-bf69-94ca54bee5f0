package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.StatusItem;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "item") 
public class Item {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; 

    @NotNull
    @Column(name = "nome", nullable = false, length = 255)
    private String nome;

    
    @NotNull
    @Column(name = "valor_unitario", nullable = false, precision = 19, scale = 2)
    private BigDecimal valorUnitario;

    // Enum para o status
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    private StatusItem status = StatusItem.ATIVO;

    // relação com processo 
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "processo_id", nullable = false)
    private Processo processo;

    @Lob
    @Column(name = "especificacao", columnDefinition = "text")
    private String especificacao;

    @NotNull
    @Column(name = "quantidade_total", nullable = false)
    private Integer quantidadeTotal = 1;

    @NotNull
    @Column(name = "quantidade_recebida", nullable = false)
    private Integer quantidadeRecebida = 0;

    public void registrarRecebimento(Integer quantidade) {
        if (quantidade == null || quantidade <= 0) {
            throw new IllegalArgumentException("Quantidade deve ser positiva");
        }

        int novaQuantidadeRecebida = this.quantidadeRecebida + quantidade;
        if (novaQuantidadeRecebida > this.quantidadeTotal) {
            throw new IllegalArgumentException("Quantidade recebida não pode exceder a quantidade total");
        }

        this.quantidadeRecebida = novaQuantidadeRecebida;
    }
}