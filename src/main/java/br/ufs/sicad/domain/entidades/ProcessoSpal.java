package br.ufs.sicad.domain.entidades;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("SPAL")
public class ProcessoSpal extends Processo {

    @OneToMany(mappedBy = "processo")
    private List<SolicitacaoSpal> solicitacoes = new ArrayList<>();
}