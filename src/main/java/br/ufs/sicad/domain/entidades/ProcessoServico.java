package br.ufs.sicad.domain.entidades;

import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("SERVICO")
public class ProcessoServico extends Processo {

    @Column(name = "prazo_vigencia_contratual")
    private Integer prazoVigenciaContratual;

    @Column(name = "unidade_medida_prazo")
    private String unidadeMedidaPrazo;

    @Column(name = "status_vigencia")
    private String statusVigencia;
}