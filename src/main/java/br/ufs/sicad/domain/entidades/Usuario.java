package br.ufs.sicad.domain.entidades;


import br.ufs.sicad.domain.enums.Status;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "usuario")
@Entity
public class Usuario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "nome", nullable = false, length = 255)
    private String nome;

    @Column(name = "sobrenome")
    private String sobrenome;

    @Column(name = "matricula", length = 50, unique = true)
    private String matricula;

    @Column(name = "cpf", length = 11, unique = true, nullable = false)
    private String cpf;

    @Column(name = "email", unique = true, nullable = false)
    private String email;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 7)
    private Status status = Status.ATIVO;

    @Column(name = "telefone", length = 20)
    private String telefone;

    @Column(name = "senha", length = 255)
    private String senha;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "unidade_organizacional_id", nullable = false)
    private UnidadeOrganizacional unidadeOrganizacional;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "perfil_id", nullable = false)
    private PerfilUsuario perfil;

    public void inativar(){
        this.status = Status.INATIVO;
    }

    public void ativar(){
        this.status = Status.ATIVO;
    }
}
