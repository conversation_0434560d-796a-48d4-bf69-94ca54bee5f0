package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "aditivo")
@Entity
public class Aditivo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "num_aditivo", nullable = false, length = 100)
    private String numAditivo;

    @Column(name = "objeto_aditivo", columnDefinition = "text")
    private String objetoAditivo;

    @Column(name = "valor_aditivo", nullable = false, precision = 18, scale = 2)
    private BigDecimal valorAditivo;

    @Column(name = "data_aditivo", nullable = false)
    private LocalDate dataAditivo;

    @Column(name = "prazo_aditivo")
    private Integer prazoAditivo;

    @Column(name = "tipo_aditivo", length = 50)
    private String tipoAditivo; // VALOR, PRAZO, VALOR_E_PRAZO

    @ManyToOne
    @JoinColumn(name = "contrato_id", nullable = false)
    private Contrato contrato;
} 