package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.entidades.Processo;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("PERMANENTE") // Valor que será salvo na coluna 'dtype'
public class ProcessoPermanente extends Processo {

}