package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "nota_empenho_item")
public class NotaEmpenhoItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nota_empenho_id", nullable = false)
    private NotaEmpenho notaEmpenho;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "item_id", nullable = false)
    private Item item;

    @NotNull(message = "A quantidade é obrigatória")
    @Positive(message = "A quantidade deve ser positiva")
    @Column(name = "quantidade", nullable = false)
    private Integer quantidade;

    @NotNull(message = "O valor unitário é obrigatório")
    @Positive(message = "O valor unitário deve ser positivo")
    @Column(name = "valor_unitario", nullable = false, precision = 15, scale = 2)
    private BigDecimal valorUnitario;

    // ***** NOVOS CAMPOS PARA LIQUIDAÇÃO *****
    @NotNull
    @Column(name = "quantidade_liquidada", nullable = false)
    private Integer quantidadeLiquidada = 0; // Inicia com zero

    @NotNull
    @Column(name = "valor_liquidado", nullable = false, precision = 15, scale = 2)
    private BigDecimal valorLiquidado = BigDecimal.ZERO; // Inicia com zero

    @Column(name = "observacoes", columnDefinition = "TEXT")
    private String observacoes;

    public NotaEmpenhoItem(NotaEmpenho notaEmpenho, Item item, Integer quantidade, BigDecimal valorUnitario) {
        this.notaEmpenho = notaEmpenho;
        this.item = item;
        this.quantidade = quantidade;
        this.valorUnitario = valorUnitario;
    }

    public BigDecimal getValorTotal() {
        if (valorUnitario == null || quantidade == null) {
            return BigDecimal.ZERO;
        }
        return valorUnitario.multiply(BigDecimal.valueOf(quantidade));
    }
}