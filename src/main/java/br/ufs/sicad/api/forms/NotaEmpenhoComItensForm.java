// src/main/java/br/ufs/sicad/api/forms/NotaEmpenhoComItensForm.java
package br.ufs.sicad.api.forms;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;
import java.util.List;

public record NotaEmpenhoComItensForm(
        @NotBlank(message = "O número é obrigatório")
        String numero,

        @NotNull(message = "A data de emissão é obrigatória")
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,

        @NotNull(message = "O prazo máximo de entrega é obrigatório")
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoMaximoEntrega,

        @NotNull(message = "Os dias de notificação prévia são obrigatórios")
        Integer diasNotificacaoPrevia,

        @NotNull(message = "O ID do fornecedor é obrigatório")
        Long fornecedorId,

        Long contratoId, // Opcional

        @NotEmpty(message = "A nota deve ter pelo menos um item")
        @Size(min = 1)
        @Valid // Garante que os itens na lista também sejam validados
        List<NotaEmpenhoItemForm> itens
) {}