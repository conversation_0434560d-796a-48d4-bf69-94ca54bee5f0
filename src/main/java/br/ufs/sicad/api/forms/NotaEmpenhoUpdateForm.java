package br.ufs.sicad.api.forms;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDate;

public record NotaEmpenhoUpdateForm(
        @NotBlank(message = "O número é obrigatório")
        String numero,
        
        @NotNull(message = "A data de emissão é obrigatória")
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,
        
        @NotNull(message = "O valor total é obrigatório")
        @Positive(message = "O valor total deve ser positivo")
        BigDecimal valorTotal,
        
        @NotNull(message = "O prazo máximo de entrega é obrigatório")
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoMaximoEntrega,
        
        @NotNull(message = "Os dias de notificação prévia são obrigatórios")
        @Positive(message = "Os dias de notificação prévia devem ser positivos")
        Integer diasNotificacaoPrevia
) {
}
