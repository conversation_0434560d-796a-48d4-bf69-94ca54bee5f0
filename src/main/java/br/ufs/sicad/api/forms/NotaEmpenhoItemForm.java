package br.ufs.sicad.api.forms;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;

// ***** ESTE É O ARQUIVO QUE PRECISA SER CORRIGIDO *****
public record NotaEmpenhoItemForm(
        @NotNull(message = "O ID do item é obrigatório")
        Long itemId,

        // ***** CORREÇÃO DEFINITIVA AQUI *****
        // O tipo deve ser Integer. O seu arquivo local provavelmente está com Long.
        @NotNull(message = "A quantidade é obrigatória")
        @Positive(message = "A quantidade deve ser positiva")
        Integer quantidade,

        @NotNull(message = "O valor unitário é obrigatório")
        @Positive(message = "O valor unitário deve ser positivo")
        BigDecimal valorUnitario,

        String observacoes
) {
}