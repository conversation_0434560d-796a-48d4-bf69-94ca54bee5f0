package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Aditivo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDate;

public record AditivoDTOForm(
        @NotBlank(message = "Número do aditivo é obrigatório")
        String numAditivo,

        String objetoAditivo,

        @NotNull(message = "Valor do aditivo é obrigatório")
        @Positive(message = "Valor do aditivo deve ser positivo")
        BigDecimal valorAditivo,

        @NotNull(message = "Data do aditivo é obrigatória")
        LocalDate dataAditivo,

        Integer prazoAditivo,

        String tipoAditivo
) {
    public Aditivo asAditivo() {
        Aditivo aditivo = new Aditivo();
        aditivo.setNumAditivo(numAditivo);
        aditivo.setObjetoAditivo(objetoAditivo);
        aditivo.setValorAditivo(valorAditivo);
        aditivo.setDataAditivo(dataAditivo);
        aditivo.setPrazoAditivo(prazoAditivo);
        aditivo.setTipoAditivo(tipoAditivo);
        return aditivo;
    }
} 