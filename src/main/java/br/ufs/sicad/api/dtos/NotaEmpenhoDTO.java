package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

public record NotaEmpenhoDTO(
        Long id,
        String numero,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,
        BigDecimal valorTotal,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoMaximoEntrega,
        Integer diasNotificacaoPrevia,
        StatusNotaEmpenho status
) {
    public static NotaEmpenhoDTO from(NotaEmpenho notaEmpenho) {
        return new NotaEmpenhoDTO(
                notaEmpenho.getId(),
                notaEmpenho.getNumero(),
                notaEmpenho.getDataEmissao(),
                notaEmpenho.getValorTotal(),
                notaEmpenho.getPrazoMaximoEntrega(),
                notaEmpenho.getDiasNotificacaoPrevia(),
                notaEmpenho.getStatus()
        );
    }
}
