package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Usuario;
import jakarta.validation.constraints.NotNull;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;


public record UsuarioDTOForm (
        String nome,
        String sobrenome,
        String matricula,
        String cpf,
        String email,
        String telefone,
        String senha,
        @NotNull Long unidadeId,
        @NotNull Long perfilId
){
    public Usuario asUsuario(){
        Usuario usuario = new Usuario();
        usuario.setNome(this.nome);
        usuario.setSobrenome(this.sobrenome);
        usuario.setMatricula(this.matricula);
        usuario.setCpf(this.cpf);
        usuario.setEmail(this.email);
        usuario.setTelefone(this.telefone);
        usuario.setSenha(new BCryptPasswordEncoder().encode(this.senha));
        return usuario;
    }
}
