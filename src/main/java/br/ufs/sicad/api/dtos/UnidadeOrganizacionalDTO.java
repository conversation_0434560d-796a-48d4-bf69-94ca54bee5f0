package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.enums.Status;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public record UnidadeOrganizacionalDTO(
        Long id,

        @NotBlank(message = "O nome é obrigatório")
        @Size(max = 100, message = "O nome deve ter no máximo 100 caracteres")
        String nome,

        @NotBlank(message = "A sigla é obrigatória")
        @Size(max = 10, message = "A sigla deve ter no máximo 10 caracteres")
        String sigla,

        Long unidadePaiId,
        
        Status status) {

    public static UnidadeOrganizacionalDTO from(UnidadeOrganizacional unidadeOrganizacional) {

        return new UnidadeOrganizacionalDTO(
                unidadeOrganizacional.getId(),
                unidadeOrganizacional.getNome(),
                unidadeOrganizacional.getSigla(),
                unidadeOrganizacional.getUnidadePai() != null ? unidadeOrganizacional.getUnidadePai().getId() : null,
                unidadeOrganizacional.getStatus());
    }
}