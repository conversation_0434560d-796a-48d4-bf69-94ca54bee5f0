package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Aditivo;

import java.math.BigDecimal;
import java.time.LocalDate;

public record AditivoDTO(
        Integer id,
        String numAditivo,
        String objetoAditivo,
        BigDecimal valorAditivo,
        LocalDate dataAditivo,
        Integer prazoAditivo,
        String tipoAditivo
) {
    public static AditivoDTO from(Aditivo aditivo) {
        return new AditivoDTO(
                aditivo.getId(),
                aditivo.getNumAditivo(),
                aditivo.getObjetoAditivo(),
                aditivo.getValorAditivo(),
                aditivo.getDataAditivo(),
                aditivo.getPrazoAditivo(),
                aditivo.getTipoAditivo()
        );
    }
} 