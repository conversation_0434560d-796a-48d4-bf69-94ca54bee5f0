package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Item;
import java.math.BigDecimal; 

public record ItemDTO(
        Long id, 
        String nome,
        BigDecimal valorUnitario, 
        String status,
        Long processoId,
        String especificacao,
        Integer quantidadeTotal,
        Integer quantidadeRecebida
) {
    public static ItemDTO from(Item item) {
        if (item == null) {
            return null;
        }
        
        return new ItemDTO(
                item.getId(),
                item.getNome(),
                item.getValorUnitario(),
                item.getStatus().name(), 
                item.getProcesso().getId(), 
                item.getEspecificacao(),
                item.getQuantidadeTotal(),
                item.getQuantidadeRecebida()
        );
    }
}