package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public record ContratoDTOForm(
        @NotBlank(message = "Número do contrato é obrigatório")
        String numContrato,

        String objetoContrato,

        @NotNull(message = "Valor total é obrigatório")
        @Positive(message = "Valor total deve ser positivo")
        BigDecimal valorTotal,

        StatusContrato status,

        @NotNull(message = "Data inicial é obrigatória")
        LocalDate dataInicial,

        LocalDate dataFinal,

        List<Long> fornecedorIds,

        List<Long> itemIds
) {
    public Contrato asContrato() {
        Contrato contrato = new Contrato();
        contrato.setNumContrato(numContrato);
        contrato.setObjetoContrato(objetoContrato);
        contrato.setValorTotal(valorTotal);
        contrato.setStatus(status != null ? status : StatusContrato.VIGENTE);
        contrato.setDataInicial(dataInicial);
        contrato.setDataFinal(dataFinal);
        return contrato;
    }
} 