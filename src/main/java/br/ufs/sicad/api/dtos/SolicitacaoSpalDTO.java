package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
import java.time.LocalDate;
import java.util.Set;

public record SolicitacaoSpalDTO(
        Long id,
        String numeroSolicitacao,
        LocalDate dataSolicitacao,
        String status,
        Integer numeroGlpi,
        UsuarioDTO solicitante,
        Long processoSpalId,
        Set<String> patrimonios
) {
    public static SolicitacaoSpalDTO from(SolicitacaoSpal solicitacao) {
        return new SolicitacaoSpalDTO(
                solicitacao.getId(),
                solicitacao.getNumeroSolicitacao(),
                solicitacao.getDataSolicitacao(),
                solicitacao.getStatus().name(),
                solicitacao.getNumeroGlpi(),
                UsuarioDTO.from(solicitacao.getSolicitante()),
                solicitacao.getProcesso().getId(),
                solicitacao.getPatrimonios()
        );
    }
}
