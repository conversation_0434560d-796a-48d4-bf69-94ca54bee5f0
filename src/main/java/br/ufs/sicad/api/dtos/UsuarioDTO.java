package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Usuario;

public record UsuarioDTO (
        Long id,
        String nome,
        String sobrenome,
        String matricula,
        String cpf,
        String email,
        String status,
        String telefone,
        UnidadeOrganizacionalDTO unidadeOrganizacional,
        PerfilUsuarioDTO perfil
){
    public static UsuarioDTO from(Usuario usuario){
        return new UsuarioDTO(
                usuario.getId(),
                usuario.getNome(),
                usuario.getSobrenome(),
                usuario.getMatricula(),
                usuario.getCpf(),
                usuario.getEmail(),
                usuario.getStatus().name(),
                usuario.getTelefone(),
                UnidadeOrganizacionalDTO.from(usuario.getUnidadeOrganizacional()),
                PerfilUsuarioDTO.from(usuario.getPerfil()));
    }
}

