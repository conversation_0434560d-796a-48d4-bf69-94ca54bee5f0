package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaEmpenhoItem;

import java.math.BigDecimal;

public record NotaEmpenhoItemDTO(
        Long id,
        ItemDTO item,
        Integer quantidade,
        BigDecimal valorUnitario,
        BigDecimal valorTotal,
        String observacoes
) {
    public static NotaEmpenhoItemDTO from(NotaEmpenhoItem notaEmpenhoItem) {
        return new NotaEmpenhoItemDTO(
                notaEmpenhoItem.getId(),
                ItemDTO.from(notaEmpenhoItem.getItem()),
                notaEmpenhoItem.getQuantidade(),
                notaEmpenhoItem.getValorUnitario(),
                notaEmpenhoItem.getValorTotal(),
                notaEmpenhoItem.getObservacoes()
        );
    }
}
