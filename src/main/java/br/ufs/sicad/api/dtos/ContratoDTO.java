package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

public record ContratoDTO(
        Integer id,
        String numContrato,
        String objetoContrato,
        BigDecimal valorTotal,
        StatusContrato status,
        LocalDate dataInicial,
        LocalDate dataFinal,
        List<FornecedorDTO> fornecedores,
        List<ItemDTO> itens,
        List<AditivoDTO> aditivos
) {
    public static ContratoDTO from(Contrato contrato) {
        List<FornecedorDTO> fornecedoresDTO = contrato.getFornecedores() != null ?
                contrato.getFornecedores().stream()
                        .map(FornecedorDTO::from)
                        .collect(Collectors.toList()) : List.of();

        List<ItemDTO> itensDTO = contrato.getItens() != null ?
                contrato.getItens().stream()
                        .map(ItemDTO::from)
                        .collect(Collectors.toList()) : List.of();

        List<AditivoDTO> aditivosDTO = contrato.getAditivos() != null ?
                contrato.getAditivos().stream()
                        .map(AditivoDTO::from)
                        .collect(Collectors.toList()) : List.of();

        return new ContratoDTO(
                contrato.getId(),
                contrato.getNumContrato(),
                contrato.getObjetoContrato(),
                contrato.getValorTotal(),
                contrato.getStatus(),
                contrato.getDataInicial(),
                contrato.getDataFinal(),
                fornecedoresDTO,
                itensDTO,
                aditivosDTO
        );
    }
} 