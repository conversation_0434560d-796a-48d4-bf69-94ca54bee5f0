package br.ufs.sicad.api.dtos;

import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public record ProcessoForm(
        // Campos Comuns
        @NotBlank String numeroSei,
        @NotNull String modalidade,
        @NotBlank String tipo,
        @NotNull(message = "A data de abertura é obrigatório.")
        String dataAbertura,
        String justificativa,
        @NotNull Long criadorId,
        @NotNull Long unidadeRequisitanteId,
        // Campos Específicos de ProcessoSPAL
        List<SolicitacaoForm> solicitacoes, // só será usado se aplicável

        // Campos Específicos de ProcessoConsumo
        String statusEntrega,

        // Campos Específicos de ProcessoServico
        Integer prazoVigenciaContratual,
        String unidadeMedidaPrazo,
        String statusVigencia
) {
    public Processo asProcesso(){
        Processo processo;

        switch (tipo.toUpperCase()){
            case "SPAL" -> {
                ProcessoSpal spal = new ProcessoSpal();
                if (solicitacoes == null || solicitacoes.isEmpty()){
                    throw new ValidationException("Processo SPAL precisa ter ao menos 1 solicitação.");
                }

                Usuario criador = new Usuario();
                criador.setId(this.criadorId);
                spal.setCriador(criador);

                UnidadeOrganizacional unidadeRequisitante = new UnidadeOrganizacional();
                unidadeRequisitante.setId(this.unidadeRequisitanteId);
                spal.setUnidadeRequisitante(unidadeRequisitante);

                spal.setSolicitacoes(this.solicitacoes.stream().map(s -> s.asSolicitacao(spal, criador)).toList());
                spal.setNumeroSei(this.numeroSei);
                spal.setModalidade(Modalidade.valueOf(this.modalidade.toUpperCase()));
                spal.setDataAbertura(Utils.converter(this.dataAbertura));
                spal.setJustificativa(this.justificativa);
                processo = spal;
            }
            default -> throw new ValidationException("Tipo de processo inválido: " + this.tipo);
        }

        return processo;
    }
}