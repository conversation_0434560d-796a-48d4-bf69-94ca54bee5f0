package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Fornecedor;

import java.util.List;

public record FornecedorDTO(
        Long id,
        String cnpj,
        String razaoSocial,
        List<String> emails,
        List<String> telefones
) {
    public static FornecedorDTO from(Fornecedor fornecedor) {
        return new FornecedorDTO(
                fornecedor.getId(),
                fornecedor.getCnpj(),
                fornecedor.getRazaoSocial(),
                fornecedor.getEmails(),
                fornecedor.getTelefones()
        );
    }
}