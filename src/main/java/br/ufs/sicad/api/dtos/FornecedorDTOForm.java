package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Fornecedor;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

public record FornecedorDTOForm(
        @NotBlank(message = "CNPJ é obrigatório")
        String cnpj,

        @NotBlank(message = "Razão social é obrigatória")
        String razaoSocial,

        List<String> emails,
        List<String> telefones
) {
    public Fornecedor asFornecedor() {
        Fornecedor fornecedor = new Fornecedor();
        fornecedor.setCnpj(cnpj);
        fornecedor.setRazaoSocial(razaoSocial);
        fornecedor.setEmails(emails);
        fornecedor.setTelefones(telefones);
        return fornecedor;
    }
}