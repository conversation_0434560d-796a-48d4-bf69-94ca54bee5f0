package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

public record NotaEmpenhoDetalhadaDTO(
        Long id,
        String numero,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,
        BigDecimal valorTotal,
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoMaximoEntrega,
        Integer diasNotificacaoPrevia,
        StatusNotaEmpenho status,
        FornecedorDTO fornecedor,
        ContratoDTO contrato,
        // ***** CORREÇÃO 1: O tipo da lista deve ser o DTO correto *****
        List<NotaEmpenhoItemDTO> itens
) {
    public static NotaEmpenhoDetalhadaDTO from(NotaEmpenho notaEmpenho) {
        return new NotaEmpenhoDetalhadaDTO(
                notaEmpenho.getId(),
                notaEmpenho.getNumero(),
                notaEmpenho.getDataEmissao(),
                notaEmpenho.getValorTotal(),
                notaEmpenho.getPrazoMaximoEntrega(),
                notaEmpenho.getDiasNotificacaoPrevia(),
                notaEmpenho.getStatus(),
                FornecedorDTO.from(notaEmpenho.getFornecedor()),
                notaEmpenho.getContrato() != null ? ContratoDTO.from(notaEmpenho.getContrato()) : null,
                notaEmpenho.getItens().stream()
                        // ***** CORREÇÃO 2: Use o método 'from' do DTO correto *****
                        .map(NotaEmpenhoItemDTO::from)
                        .collect(Collectors.toList()) // Usar .collect() é mais comum que .toList() em algumas versões
        );
    }
}