package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.ProcessoSpal;
import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.domain.enums.StatusSolicitacaoSpal;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.constraints.NotBlank;

import java.util.Set;

public record SolicitacaoForm(
    String numeroSolicitacao,
    @NotBlank(message = "A data da solicitação é obrigatório.")
    String dataSolicitacao,
    Long solicitanteId,
    @NotBlank(message = "O status da solicitação é obrigatório.")
    String status,
    Set<String> patrimonios
){
    public SolicitacaoSpal asSolicitacao(ProcessoSpal processo, Usuario solicitante){
        SolicitacaoSpal solicitacao = new SolicitacaoSpal();
        solicitacao.setNumeroSolicitacao(this.numeroSolicitacao);
        solicitacao.setDataSolicitacao(Utils.converter(this.dataSolicitacao));
        solicitacao.setStatus(StatusSolicitacaoSpal.valueOf(status.toUpperCase()));
        solicitacao.setPatrimonios(Set.copyOf(patrimonios));
        solicitacao.setSolicitante(solicitante);
        solicitacao.setProcesso(processo);
        return solicitacao;
    }
}
