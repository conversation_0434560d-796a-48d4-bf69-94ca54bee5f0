package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.NotaEmpenhoDTO;
import br.ufs.sicad.api.dtos.NotaEmpenhoDetalhadaDTO;
import br.ufs.sicad.api.forms.NotaEmpenhoComItensForm;
import br.ufs.sicad.api.forms.NotaEmpenhoUpdateForm;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.services.NotaEmpenhoService;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@RestController
@RequestMapping("/notas-empenho")
@RequiredArgsConstructor
public class NotaEmpenhoController {

    private final NotaEmpenhoService notaEmpenhoService;

    @GetMapping
    public ResponseEntity<Page<NotaEmpenhoDTO>> listarTodos(
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String dataEmissaoInicio,
            @RequestParam(required = false) String dataEmissaoFim,
            @RequestParam(required = false) BigDecimal valorMinimo,
            @RequestParam(required = false) BigDecimal valorMaximo,
            Pageable pageable) {
        
        LocalDate dataInicio = dataEmissaoInicio != null ? Utils.converter(dataEmissaoInicio) : null;
        LocalDate dataFim = dataEmissaoFim != null ? Utils.converter(dataEmissaoFim) : null;
        
        Page<NotaEmpenho> notasEmpenho = notaEmpenhoService.listarComFiltros(numero, dataInicio, dataFim,
                                                                 valorMinimo, valorMaximo, pageable);
        Page<NotaEmpenhoDTO> dtos = notasEmpenho.map(NotaEmpenhoDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> buscarPorId(@PathVariable Long id) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.buscarPorId(id);
        return ResponseEntity.ok(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @PostMapping
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> criar(@RequestBody @Valid NotaEmpenhoComItensForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.criarComItens(form);
        return ResponseEntity.status(HttpStatus.CREATED).body(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @PutMapping("/{id}")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> atualizar(@PathVariable Long id,
                                                             @RequestBody @Valid NotaEmpenhoUpdateForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.atualizar(
                id,
                form.numero(),
                form.dataEmissao(),
                form.valorTotal(),
                form.prazoMaximoEntrega(),
                form.diasNotificacaoPrevia()
        );

        return ResponseEntity.ok(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletar(@PathVariable Long id) {
        notaEmpenhoService.deletar(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/reativar")
    public ResponseEntity<Void> reativar(@PathVariable Long id) {
        notaEmpenhoService.reativar(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/anular-total")
    public ResponseEntity<Void> anularEmpenhoTotal(@PathVariable Long id) {
        notaEmpenhoService.anularEmpenhoTotal(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/anular-parcial")
    public ResponseEntity<Void> anularEmpenhoTotal(@PathVariable Long id, 
                                                  @RequestParam BigDecimal valor) {
        notaEmpenhoService.anularEmpenhoTotal(id, valor);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/vencimento")
    public ResponseEntity<Page<NotaEmpenhoDetalhadaDTO>> buscarPorPrazoVencimento(
            @RequestParam String dataLimite,
            Pageable pageable) {
        
        LocalDate data = Utils.converter(dataLimite);
        Page<NotaEmpenho> notasEmpenho = notaEmpenhoService.buscarPorPrazoVencimento(data, pageable);
        Page<NotaEmpenhoDetalhadaDTO> dtos = notasEmpenho.map(NotaEmpenhoDetalhadaDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}/numero")
    public ResponseEntity<String> getNumero(@PathVariable Long id) {
        return ResponseEntity.ok(notaEmpenhoService.getNumero(id));
    }

    @PutMapping("/{id}/numero")
    public ResponseEntity<Void> setNumero(@PathVariable Long id, @RequestParam String numero) {
        notaEmpenhoService.setNumero(id, numero);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/data-emissao")
    public ResponseEntity<LocalDate> getDataEmissao(@PathVariable Long id) {
        return ResponseEntity.ok(notaEmpenhoService.getDataEmissao(id));
    }

    @PutMapping("/{id}/data-emissao")
    public ResponseEntity<Void> setDataEmissao(@PathVariable Long id, @RequestParam String data) {
        notaEmpenhoService.setDataEmissao(id, Utils.converter(data));
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/valor-total")
    public ResponseEntity<BigDecimal> getValorTotal(@PathVariable Long id) {
        return ResponseEntity.ok(notaEmpenhoService.getValorTotal(id));
    }

    @PutMapping("/{id}/valor-total")
    public ResponseEntity<Void> setValorTotal(@PathVariable Long id, @RequestParam BigDecimal valor) {
        notaEmpenhoService.setValorTotal(id, valor);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/prazo-maximo-entrega")
    public ResponseEntity<LocalDate> getPrazoMaximoEntrega(@PathVariable Long id) {
        return ResponseEntity.ok(notaEmpenhoService.getPrazoMaximoEntrega(id));
    }

    @PutMapping("/{id}/prazo-maximo-entrega")
    public ResponseEntity<Void> setPrazoMaximoEntrega(@PathVariable Long id, @RequestParam String data) {
        notaEmpenhoService.setPrazoMaximoEntrega(id, Utils.converter(data));
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/dias-notificacao-previa")
    public ResponseEntity<Integer> getDiasNotificacaoPrevia(@PathVariable Long id) {
        return ResponseEntity.ok(notaEmpenhoService.getDiasNotificacaoPrevia(id));
    }

    @PutMapping("/{id}/dias-notificacao-previa")
    public ResponseEntity<Void> setDiasNotificacaoPrevia(@PathVariable Long id, @RequestParam Integer dias) {
        notaEmpenhoService.setDiasNotificacaoPrevia(id, dias);
        return ResponseEntity.ok().build();
    }
}
