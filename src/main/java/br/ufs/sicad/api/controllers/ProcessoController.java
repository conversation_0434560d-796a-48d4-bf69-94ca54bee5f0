package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.ProcessoDTO;
import br.ufs.sicad.api.dtos.ProcessoForm;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.services.ProcessoService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;


@RestController
@RequestMapping("processos")
public class ProcessoController {

    private final ProcessoService processoService;

    public ProcessoController(ProcessoService processoService) {
        this.processoService = processoService;
    }

    @GetMapping
    public ResponseEntity<List<ProcessoDTO>> listarProcessos() {
        List<ProcessoDTO> processos = processoService.listarProcessos().stream().map(ProcessoDTO::from).toList();
        return ResponseEntity.status(HttpStatus.OK).body(processos);
    }

    @PostMapping
    public ResponseEntity<ProcessoDTO> criar(@RequestBody ProcessoForm processoForm) {
        Processo processoSalvo = processoService.criarProcesso(processoForm.asProcesso());
        return ResponseEntity.status(HttpStatus.CREATED).body(ProcessoDTO.from(processoSalvo));
    }
}