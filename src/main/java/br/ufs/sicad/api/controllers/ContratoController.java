package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.ContratoDTO;
import br.ufs.sicad.api.dtos.ContratoDTOForm;
import br.ufs.sicad.api.dtos.ContratoDTOUpdateForm;
import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.services.ContratoService;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/contratos")
public class ContratoController {

    private final ContratoService contratoService;

    public ContratoController(ContratoService contratoService) {
        this.contratoService = contratoService;
    }

    @PostMapping
    public ResponseEntity<ContratoDTO> criarContrato(@RequestBody @Valid ContratoDTOForm contratoForm) {
        Contrato contrato = contratoService.criarContrato(
                contratoForm.asContrato(),
                contratoForm.fornecedorIds(),
                contratoForm.itemIds()
        );
        return ResponseEntity.status(HttpStatus.CREATED).body(ContratoDTO.from(contrato));
    }

    @GetMapping
    public ResponseEntity<Page<ContratoDTO>> listarContratos(
            @RequestParam(required = false) String numContrato,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) LocalDate dataInicial,
            @RequestParam(required = false) LocalDate dataFinal,
            @RequestParam(required = false) List<Long> fornecedorIds,
            @RequestParam(required = false) List<Integer> itemIds,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "30") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Contrato> contratos = contratoService.listarContratos(
                numContrato, status, dataInicial, dataFinal, fornecedorIds, itemIds, pageable);

        List<ContratoDTO> contratosDTO = contratos.getContent().stream()
                .map(ContratoDTO::from)
                .collect(Collectors.toList());

        Page<ContratoDTO> contratosPage = new PageImpl<>(contratosDTO, pageable, contratos.getTotalElements());

        return ResponseEntity.ok(contratosPage);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ContratoDTO> buscarContrato(@PathVariable Integer id) {
        Contrato contrato = contratoService.buscarContratoPor(id);
        return ResponseEntity.ok(ContratoDTO.from(contrato));
    }

    @GetMapping("/fornecedor/{fornecedorId}")
    public ResponseEntity<List<ContratoDTO>> buscarContratosPorFornecedor(@PathVariable Long fornecedorId) {
        List<Contrato> contratos = contratoService.buscarContratosPorFornecedor(fornecedorId);
        List<ContratoDTO> contratosDTO = contratos.stream()
                .map(ContratoDTO::from)
                .collect(Collectors.toList());
        return ResponseEntity.ok(contratosDTO);
    }

    @GetMapping("/{contratoId}/itens")
    public ResponseEntity<ContratoDTO> buscarItensDoContrato(@PathVariable Integer contratoId) {
        Contrato contrato = contratoService.buscarContratoPor(contratoId);
        return ResponseEntity.ok(ContratoDTO.from(contrato));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ContratoDTO> atualizarContrato(
            @PathVariable Integer id,
            @RequestBody @Valid ContratoDTOUpdateForm contratoForm) {
        Contrato contrato = contratoService.atualizarContrato(
                id,
                contratoForm.asContrato(),
                contratoForm.fornecedorIds(),
                contratoForm.itemIds()
        );
        return ResponseEntity.ok(ContratoDTO.from(contrato));
    }
} 