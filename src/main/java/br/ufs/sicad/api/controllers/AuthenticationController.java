package br.ufs.sicad.api.controllers;

import br.ufs.sicad.infrastructure.security.AuthenticationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AuthenticationController {
    @Autowired
    private final AuthenticationService authenticationService;

    public AuthenticationController(AuthenticationService authenticationService) {
        this.authenticationService = authenticationService;
    }

    @PostMapping("autenticar")
    public String autenticar(Authentication authentication){
        return authenticationService.authenticate(authentication);
    }
}
