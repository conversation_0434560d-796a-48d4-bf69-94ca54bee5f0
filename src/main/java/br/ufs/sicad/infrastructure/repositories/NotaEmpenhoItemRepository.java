package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.NotaEmpenhoItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotaEmpenhoItemRepository extends JpaRepository<NotaEmpenhoItem, Long> {
    
    @Query("SELECT nei FROM NotaEmpenhoItem nei WHERE nei.notaEmpenho.id = :notaEmpenhoId")
    List<NotaEmpenhoItem> findByNotaEmpenhoId(@Param("notaEmpenhoId") Long notaEmpenhoId);
    
    @Query("SELECT nei FROM NotaEmpenhoItem nei WHERE nei.item.id = :itemId")
    List<NotaEmpenhoItem> findByItemId(@Param("itemId") Long itemId);
}
