package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.Fornecedor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FornecedorRepository extends JpaRepository<Fornecedor, Long> {
    
    Optional<Fornecedor> findByCnpj(String cnpj);
    
    @Query("SELECT f FROM Fornecedor f WHERE " +
           "(:cnpj IS NULL OR f.cnpj LIKE %:cnpj%) AND " +
           "(:razaoSocial IS NULL OR f.razaoSocial LIKE %:razaoSocial%)")
    Page<Fornecedor> findByFilters(@Param("cnpj") String cnpj, 
                                   @Param("razaoSocial") String razaoSocial, 
                                   Pageable pageable);
}