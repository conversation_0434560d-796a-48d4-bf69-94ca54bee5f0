package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;

import br.ufs.sicad.domain.enums.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UnidadeOrganizacionalRepository extends JpaRepository<UnidadeOrganizacional, Long> {
        boolean existsBySigla(String sigla);

        boolean existsByUnidadePaiId(Long unidadePaiId);

        boolean existsBySiglaAndStatus(String sigla, Status status);

        Page<UnidadeOrganizacional> findByStatus(Status status, Pageable pageable);

        @Query("SELECT u FROM UnidadeOrganizacional u " +
                        "WHERE " +
                        "(:nome IS NULL OR u.nome ILIKE '%' || CAST(:nome AS string) || '%') AND " +
                        "(:sigla IS NULL OR u.sigla ILIKE '%' || CAST(:sigla AS string) || '%') AND " +
                        "(:status IS NULL OR u.status = :status)")
        Page<UnidadeOrganizacional> findByFilters(
                        @Param("nome") String nome,
                        @Param("sigla") String sigla,
                        @Param("status") Status status,
                        Pageable pageable);
}