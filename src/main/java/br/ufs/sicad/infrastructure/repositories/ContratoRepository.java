package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.enums.StatusContrato;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface ContratoRepository extends JpaRepository<Contrato, Integer> {

       Optional<Contrato> findByNumContrato(String numContrato);

       @Query("SELECT c FROM Contrato c WHERE " +
                     "(:numContrato IS NULL OR c.numContrato LIKE %:numContrato%) AND " +
                     "(:status IS NULL OR c.status = :status) AND " +
                     "(:dataInicial IS NULL OR c.dataInicial >= :dataInicial) AND " +
                     "(:dataFinal IS NULL OR c.dataFinal <= :dataFinal)")
       Page<Contrato> findByFilters(@Param("numContrato") String numContrato,
                     @Param("status") String status,
                     @Param("dataInicial") LocalDate dataInicial,
                     @Param("dataFinal") LocalDate dataFinal,
                     Pageable pageable);

       @Query("SELECT c FROM Contrato c JOIN c.fornecedores f WHERE f.id = :fornecedorId AND c.status = :status")
       List<Contrato> findByFornecedoresIdAndStatus(@Param("fornecedorId") Long fornecedorId,
                                                     @Param("status") StatusContrato status);
}