package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

@Repository
public interface NotaEmpenhoRepository extends JpaRepository<NotaEmpenho, Long> {

    // Buscar apenas notas ativas (incluindo registros sem status definido)
    @Query("SELECT n FROM NotaEmpenho n WHERE n.status IS NULL OR n.status = 'ATIVA'")
    Page<NotaEmpenho> findAllActive(Pageable pageable);

    // Buscar por número apenas entre notas ativas
    @Query("SELECT n FROM NotaEmpenho n WHERE n.numero = :numero AND (n.status IS NULL OR n.status = 'ATIVA')")
    Optional<NotaEmpenho> findByNumeroAndActive(@Param("numero") String numero);

    // Buscar por ID apenas entre notas ativas
    @Query("SELECT n FROM NotaEmpenho n WHERE n.id = :id AND (n.status IS NULL OR n.status = 'ATIVA')")
    Optional<NotaEmpenho> findByIdAndActive(@Param("id") Long id);

    // Métodos originais mantidos para compatibilidade (incluem inativas)
    Optional<NotaEmpenho> findByNumero(String numero);

    boolean existsByNumero(String numero);

    // Verificar se existe número apenas entre notas ativas
    @Query("SELECT COUNT(n) > 0 FROM NotaEmpenho n WHERE n.numero = :numero AND (n.status IS NULL OR n.status = 'ATIVA')")
    boolean existsByNumeroAndActive(@Param("numero") String numero);

    @Query("SELECT n FROM NotaEmpenho n WHERE " +
           "(n.status IS NULL OR n.status = 'ATIVA') AND " +
           "(:numero IS NULL OR n.numero LIKE %:numero%) AND " +
           "(:dataEmissaoInicio IS NULL OR n.dataEmissao >= :dataEmissaoInicio) AND " +
           "(:dataEmissaoFim IS NULL OR n.dataEmissao <= :dataEmissaoFim) AND " +
           "(:valorMinimo IS NULL OR n.valorTotal >= :valorMinimo) AND " +
           "(:valorMaximo IS NULL OR n.valorTotal <= :valorMaximo)")
    Page<NotaEmpenho> findByFilters(
            @Param("numero") String numero,
            @Param("dataEmissaoInicio") LocalDate dataEmissaoInicio,
            @Param("dataEmissaoFim") LocalDate dataEmissaoFim,
            @Param("valorMinimo") BigDecimal valorMinimo,
            @Param("valorMaximo") BigDecimal valorMaximo,
            Pageable pageable);

    @Query("SELECT n FROM NotaEmpenho n WHERE n.prazoMaximoEntrega <= :dataLimite AND (n.status IS NULL OR n.status = 'ATIVA')")
    Page<NotaEmpenho> findByPrazoMaximoEntregaLessThanEqual(
            @Param("dataLimite") LocalDate dataLimite,
            Pageable pageable);
}
