package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.Item;
import br.ufs.sicad.infrastructure.repositories.ItemRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ItemService {

    private final ItemRepository itemRepository;

    public Page<Item> listarItens(Pageable pageable) {
        return itemRepository.findAll(pageable);
    }

    public Item buscarPorId(Long id) {
        return itemRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Item não encontrado. ID: " + id));
    }

    public Item criarItem(Item item) {
        return itemRepository.save(item);
    }

    public Item atualizarItem(Long id, Item itemAtualizado) {
        Item item = buscarPorId(id);
        item.setNome(itemAtualizado.getNome());
        item.setValorUnitario(itemAtualizado.getValorUnitario());
        item.setStatus(itemAtualizado.getStatus());
        item.setEspecificacao(itemAtualizado.getEspecificacao());
        item.setQuantidadeTotal(itemAtualizado.getQuantidadeTotal());
        return itemRepository.save(item);
    }

    public void deletarItem(Long id) {
        buscarPorId(id);
        itemRepository.deleteById(id);
    }

    public List<Item> listarTodosItens() {
        return itemRepository.findAll();
    }

    public List<Item> buscarPorIds(List<Long> ids) {
        return itemRepository.findByIdIn(ids);
    }

    public void registrarRecebimento(Long id, Integer quantidade) {
        Item item = buscarPorId(id);
        item.registrarRecebimento(quantidade);
        itemRepository.save(item);
    }
}
