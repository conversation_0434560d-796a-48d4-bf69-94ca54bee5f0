package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.Fornecedor;
import br.ufs.sicad.infrastructure.repositories.FornecedorRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FornecedorService {
    
    private final FornecedorRepository fornecedorRepository;

    public FornecedorService(FornecedorRepository fornecedorRepository) {
        this.fornecedorRepository = fornecedorRepository;
    }

    public Page<Fornecedor> listarFornecedores(String cnpj, String razaoSocial, Pageable pageable) {
        return fornecedorRepository.findByFilters(cnpj, razaoSocial, pageable);
    }

    public Fornecedor criarFornecedor(Fornecedor fornecedor) {
        return fornecedorRepository.save(fornecedor);
    }

    public Fornecedor buscarFornecedorPor(Long id) {
        return fornecedorRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Fornecedor não encontrado. ID: " + id));
    }

    public Fornecedor atualizarFornecedor(Long id, String cnpj, String razaoSocial, 
                                         List<String> emails, List<String> telefones) {
        Fornecedor fornecedor = buscarFornecedorPor(id);
        fornecedor.setCnpj(cnpj);
        fornecedor.setRazaoSocial(razaoSocial);
        fornecedor.setEmails(emails);
        fornecedor.setTelefones(telefones);
        return fornecedorRepository.save(fornecedor);
    }

    public void deletarFornecedor(Long id) {
        buscarFornecedorPor(id);
        fornecedorRepository.deleteById(id);
    }

    public List<Fornecedor> listarTodosFornecedores() {
        return fornecedorRepository.findAll();
    }
}