package br.ufs.sicad.services;

import br.ufs.sicad.config.PasswordValidationException;
import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.PerfilUsuario;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.infrastructure.repositories.PerfilUsuarioRepository;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;


@Service
public class UsuarioService {
    @Autowired
    private final UsuarioRepository usuarioRepository;
    @Autowired
    private final PerfilUsuarioRepository perfilUsuarioRepository;
    @Autowired
    private final UnidadeOrganizacionalRepository unidadeOrganizacionalRepository;


    public UsuarioService(UsuarioRepository usuarioRepository, PerfilUsuarioRepository perfilUsuarioRepository, UnidadeOrganizacionalRepository unidadeOrganizacionalRepository) {
        this.usuarioRepository = usuarioRepository;
        this.perfilUsuarioRepository = perfilUsuarioRepository;
        this.unidadeOrganizacionalRepository = unidadeOrganizacionalRepository;
    }

    public Page<Usuario> listarUsuarios(String nome, String matricula, String cpf, String email, 
        String telefone, Long unidadeOrganizacionalId, Long perfilId, Pageable pageable) {
    return usuarioRepository.findByFilters(nome, matricula, cpf, email, telefone, 
            unidadeOrganizacionalId, perfilId, pageable);
}

    public Usuario buscarUsuarioPor(Long UsuarioId) {
        return usuarioRepository.findById(UsuarioId)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado. - Usuário ID: " + UsuarioId));
    }

    public Usuario buscarUsuarioPor(String email){
        return usuarioRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado. - Email: " + email));
    }

    public Usuario criarUsuario(Usuario usuario, Long perfilId, Long unidadeId) {
        if (perfilId == null || unidadeId == null) {
            throw new ValidationException("Campos Perfil e Unidade são obrigatórios.");
        }
        PerfilUsuario perfil = perfilUsuarioRepository.findById(perfilId)
                .orElseThrow(() -> new ResourceNotFoundException("Perfil não encontrado: " + perfilId));
        usuario.setPerfil(perfil);

        UnidadeOrganizacional unidade = unidadeOrganizacionalRepository.findById(unidadeId)
                    .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada: " + unidadeId));
        usuario.setUnidadeOrganizacional(unidade);
        return usuarioRepository.save(usuario);
    }

    public Usuario atualizarUsuario(Long id, String matricula, String novoNome,String novoSobrenome, String novoEmail, String novoCpf, String novoTelefone, Long perfilId, Long unidadeId) {
        Usuario usuario = buscarUsuarioPor(id);
        usuario.setMatricula(matricula);
        usuario.setNome(novoNome);
        usuario.setSobrenome(novoSobrenome);
        usuario.setEmail(novoEmail);
        usuario.setCpf(novoCpf);
        usuario.setTelefone(novoTelefone);
        UnidadeOrganizacional unidade = null;
        if (unidadeId != null) {
            unidade = unidadeOrganizacionalRepository.findById(unidadeId)
                .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada: " + unidadeId));
        }
        usuario.setUnidadeOrganizacional(unidade);

        PerfilUsuario perfil = perfilUsuarioRepository.findById(perfilId)
                .orElseThrow(() -> new ResourceNotFoundException("Perfil não encontrado: " + perfilId));
        usuario.setPerfil(perfil);

        return usuarioRepository.save(usuario);
    }

    public void alterarSenha(Long id, String senhaAntiga, String senhaNova){
        Usuario usuario = buscarUsuarioPor(id);
        if (!BCrypt.checkpw(senhaAntiga, usuario.getSenha())){
            throw new PasswordValidationException("Senha atual inválida.");
        }
        usuario.setSenha(new BCryptPasswordEncoder().encode(senhaNova));
        usuarioRepository.save(usuario);
    }

    public void recuperarSenha(Long id){
        Usuario usuario = buscarUsuarioPor(id);
        usuario.setSenha(new BCryptPasswordEncoder().encode(usuario.getCpf().substring(0,6)));
        usuarioRepository.save(usuario);
    }

    public void inativarUsuario(Long id) {
        Usuario usuario = buscarUsuarioPor(id);
        usuario.inativar();
        usuarioRepository.save(usuario);
    }

    public Usuario reativarUsuairo(Long id){
        Usuario usuario = buscarUsuarioPor(id);
        usuario.ativar();
        return usuarioRepository.save(usuario);
    }
}