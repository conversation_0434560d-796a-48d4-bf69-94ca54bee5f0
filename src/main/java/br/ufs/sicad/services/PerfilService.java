package br.ufs.sicad.services;

import br.ufs.sicad.domain.entidades.PerfilUsuario;
import br.ufs.sicad.infrastructure.repositories.PerfilUsuarioRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PerfilService {

    @Autowired
    private final PerfilUsuarioRepository perfilUsuarioRepository;

    public PerfilService(PerfilUsuarioRepository perfilUsuarioRepository) {
        this.perfilUsuarioRepository = perfilUsuarioRepository;
    }

    public List<PerfilUsuario> listarUsuarios(){
        return perfilUsuarioRepository.findAll();
    }
}
