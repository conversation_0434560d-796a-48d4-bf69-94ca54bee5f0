package br.ufs.sicad.services;

import br.ufs.sicad.api.forms.NotaEmpenhoComItensForm;
import br.ufs.sicad.api.forms.NotaEmpenhoItemForm;
import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.UniqueResourceViolationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.infrastructure.repositories.NotaEmpenhoRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;

@Service
@RequiredArgsConstructor
public class NotaEmpenhoService {

    private final NotaEmpenhoRepository repository;
    private final FornecedorService fornecedorService;
    private final ContratoService contratoService;
    private final ItemService itemService;

    public Page<NotaEmpenho> listarTodos(Pageable pageable) {
        return repository.findAllActive(pageable);
    }

    public Page<NotaEmpenho> listarComFiltros(String numero, LocalDate dataEmissaoInicio, 
                                              LocalDate dataEmissaoFim, BigDecimal valorMinimo, 
                                              BigDecimal valorMaximo, Pageable pageable) {
        return repository.findByFilters(numero, dataEmissaoInicio, dataEmissaoFim, 
                                       valorMinimo, valorMaximo, pageable);
    }

    public NotaEmpenho buscarPorId(Long id) {
        return repository.findByIdAndActive(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nota de Empenho não encontrada"));
    }

    public NotaEmpenho getById(Long id) {
        return buscarPorId(id);
    }

    public String getNumero(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        return notaEmpenho.getNumero();
    }

    public void setNumero(Long id, String numero) {
        if (repository.existsByNumero(numero)) {
            throw new UniqueResourceViolationException("Já existe uma nota de empenho com este número");
        }
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.setNumero(numero);
        repository.save(notaEmpenho);
    }

    public LocalDate getDataEmissao(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        return notaEmpenho.getDataEmissao();
    }

    public void setDataEmissao(Long id, LocalDate data) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.setDataEmissao(data);
        repository.save(notaEmpenho);
    }

    public BigDecimal getValorTotal(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        return notaEmpenho.getValorTotal();
    }

    public void setValorTotal(Long id, BigDecimal valor) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.setValorTotal(valor);
        repository.save(notaEmpenho);
    }

    public LocalDate getPrazoMaximoEntrega(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        return notaEmpenho.getPrazoMaximoEntrega();
    }

    public void setPrazoMaximoEntrega(Long id, LocalDate data) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.setPrazoMaximoEntrega(data);
        repository.save(notaEmpenho);
    }

    public Integer getDiasNotificacaoPrevia(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        return notaEmpenho.getDiasNotificacaoPrevia();
    }

    public void setDiasNotificacaoPrevia(Long id, Integer dias) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.setDiasNotificacaoPrevia(dias);
        repository.save(notaEmpenho);
    }



    @Transactional
    public NotaEmpenho criarComItens(NotaEmpenhoComItensForm form) {
        // Validações de regra de negócio
        if (repository.existsByNumeroAndActive(form.numero())) {
            throw new UniqueResourceViolationException("Já existe uma nota de empenho ativa com este número");
        }

        if (form.fornecedorId() == null) {
            throw new IllegalArgumentException("Fornecedor é obrigatório para criar uma nota de empenho");
        }

        if (form.itensIds() == null || form.itensIds().isEmpty()) {
            throw new IllegalArgumentException("Uma nota de empenho deve ter pelo menos um item");
        }

        // Buscar fornecedor
        Fornecedor fornecedor = fornecedorService.buscarFornecedorPor(form.fornecedorId());

        // Buscar contrato se informado
        Contrato contrato = null;
        if (form.contratoId() != null) {
            contrato = contratoService.buscarContratoPor(form.contratoId());
        }

        // Criar nota de empenho
        NotaEmpenho notaEmpenho = new NotaEmpenho(
                form.numero(),
                form.dataEmissao(),
                form.prazoMaximoEntrega(),
                form.diasNotificacaoPrevia(),
                fornecedor
        );

        if (contrato != null) {
            notaEmpenho.setContrato(contrato);
        }

        // Adicionar itens
        BigDecimal valorTotal = BigDecimal.ZERO;
        for (Long itemId : form.itensIds()) {
            Item item = itemService.buscarPorId(itemId);
            item.setNotaEmpenho(notaEmpenho);
            itemService.atualizarItem(item.getId(), item);
            notaEmpenho.adicionarItem(item);
            valorTotal = valorTotal.add(item.getValorUnitario());
        }

        notaEmpenho.setValorTotal(valorTotal);

        // Validar regras de negócio antes de salvar
        notaEmpenho.validarRegrasDeNegocio();

        return repository.save(notaEmpenho);
    }

    @Transactional
    public NotaEmpenho atualizar(Long id, String numero, LocalDate dataEmissao, BigDecimal valorTotal,
                                LocalDate prazoMaximoEntrega, Integer diasNotificacaoPrevia) {
        NotaEmpenho notaEmpenho = buscarPorId(id);

        if (!notaEmpenho.getNumero().equals(numero) && repository.existsByNumeroAndActive(numero)) {
            throw new UniqueResourceViolationException("Já existe uma nota de empenho ativa com este número");
        }

        notaEmpenho.setNumero(numero);
        notaEmpenho.setDataEmissao(dataEmissao);
        notaEmpenho.setValorTotal(valorTotal);
        notaEmpenho.setPrazoMaximoEntrega(prazoMaximoEntrega);
        notaEmpenho.setDiasNotificacaoPrevia(diasNotificacaoPrevia);

        return repository.save(notaEmpenho);
    }

    @Transactional
    public void anularEmpenhoTotal(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.anularEmpenhoTotal();
        repository.save(notaEmpenho);
    }

    @Transactional
    public void anularEmpenhoTotal(Long id, BigDecimal valor) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.anularEmpenhoTotal(valor);
        repository.save(notaEmpenho);
    }

    @Transactional
    public void deletar(Long id) {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        notaEmpenho.inativar();
        repository.save(notaEmpenho);
    }

    @Transactional
    public void reativar(Long id) {
        // Busca incluindo inativas para permitir reativação
        NotaEmpenho notaEmpenho = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Nota de Empenho não encontrada"));
        notaEmpenho.ativar();
        repository.save(notaEmpenho);
    }

    public Page<NotaEmpenho> buscarPorPrazoVencimento(LocalDate dataLimite, Pageable pageable) {
        return repository.findByPrazoMaximoEntregaLessThanEqual(dataLimite, pageable);
    }
}
