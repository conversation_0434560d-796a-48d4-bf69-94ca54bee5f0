package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.infrastructure.repositories.ProcessoRepository;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class ProcessoService {

    private final ProcessoRepository processoRepository;
    private final UsuarioRepository usuarioRepository;
    private final UnidadeOrganizacionalRepository unidadeRepository;

    public ProcessoService(ProcessoRepository processoRepository, UsuarioRepository usuarioRepository, UnidadeOrganizacionalRepository unidadeRepository) {
        this.processoRepository = processoRepository;
        this.usuarioRepository = usuarioRepository;
        this.unidadeRepository = unidadeRepository;
    }

    // Método para LISTAR
    public List<Processo> listarProcessos() {
        return processoRepository.findAll();
    }

    // --- MÉTODO PARA BUSCAR POR ID ---
    public Processo buscarProcessoPorId(Long id) {
        return processoRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Processo com ID " + id + " não encontrado."));
    }

    // --- MÉTODO PARA CRIAR PROCESSO ---
    @Transactional
    public Processo criarProcesso(Processo novoProcesso) {
        Usuario criador = usuarioRepository.findById(novoProcesso.getCriador().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Usuário criador não encontrado."));

        UnidadeOrganizacional unidade = unidadeRepository.findById(novoProcesso.getUnidadeRequisitante().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Unidade requisitante não encontrada."));

        novoProcesso.setCriador(criador);
        novoProcesso.setUnidadeRequisitante(unidade);

        return processoRepository.save(novoProcesso);
    }
}