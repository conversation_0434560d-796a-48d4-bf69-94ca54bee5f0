package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.UniqueResourceViolationException;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.infrastructure.repositories.NotaEmpenhoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotaEmpenhoServiceTest {

    @Mock
    private NotaEmpenhoRepository repository;

    @InjectMocks
    private NotaEmpenhoService service;

    private NotaEmpenho notaEmpenho;

    @BeforeEach
    void setUp() {
        notaEmpenho = new NotaEmpenho("2024001", LocalDate.now(), LocalDate.now().plusDays(30), 5);
        notaEmpenho.setId(1L);
        notaEmpenho.setValorTotal(new BigDecimal("1000.00"));
    }

    @Test
    void deveCriarNotaEmpenhoComSucesso() {
        // Given
        when(repository.existsByNumero("2024001")).thenReturn(false);
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        NotaEmpenho resultado = service.criar("2024001", LocalDate.now(), 
                                            new BigDecimal("1000.00"), 
                                            LocalDate.now().plusDays(30), 5);

        // Then
        assertNotNull(resultado);
        assertEquals("2024001", resultado.getNumero());
        assertEquals(new BigDecimal("1000.00"), resultado.getValorTotal());
        verify(repository).save(any(NotaEmpenho.class));
    }

    @Test
    void deveLancarExcecaoAoCriarComNumeroExistente() {
        // Given
        when(repository.existsByNumero("2024001")).thenReturn(true);

        // When & Then
        assertThrows(UniqueResourceViolationException.class, () -> {
            service.criar("2024001", LocalDate.now(), 
                         new BigDecimal("1000.00"), 
                         LocalDate.now().plusDays(30), 5);
        });
    }

    @Test
    void deveBuscarNotaEmpenhoPorId() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));

        // When
        NotaEmpenho resultado = service.buscarPorId(1L);

        // Then
        assertNotNull(resultado);
        assertEquals(1L, resultado.getId());
        assertEquals("2024001", resultado.getNumero());
    }

    @Test
    void deveLancarExcecaoAoBuscarNotaEmpenhoInexistente() {
        // Given
        when(repository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            service.buscarPorId(999L);
        });
    }

    @Test
    void deveRetornarNumeroCorretamente() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));

        // When
        String numero = service.getNumero(1L);

        // Then
        assertEquals("2024001", numero);
    }

    @Test
    void deveAtualizarNumeroCorretamente() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.existsByNumero("2024002")).thenReturn(false);
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.setNumero(1L, "2024002");

        // Then
        verify(repository).save(notaEmpenho);
    }

    @Test
    void deveRetornarValorTotalCorretamente() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));

        // When
        BigDecimal valor = service.getValorTotal(1L);

        // Then
        assertEquals(new BigDecimal("1000.00"), valor);
    }

    @Test
    void deveAtualizarValorTotalCorretamente() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.setValorTotal(1L, new BigDecimal("2000.00"));

        // Then
        verify(repository).save(notaEmpenho);
    }

    @Test
    void deveAnularEmpenhoTotal() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.anularEmpenhoTotal(1L);

        // Then
        verify(repository).save(notaEmpenho);
    }

    @Test
    void deveAnularEmpenhoParcial() {
        // Given
        when(repository.findById(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.anularEmpenhoTotal(1L, new BigDecimal("500.00"));

        // Then
        verify(repository).save(notaEmpenho);
    }
}
