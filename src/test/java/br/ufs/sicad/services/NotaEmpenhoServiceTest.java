package br.ufs.sicad.services;

import br.ufs.sicad.api.forms.NotaEmpenhoComItensForm;
import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.UniqueResourceViolationException;
import br.ufs.sicad.domain.entidades.Fornecedor;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.infrastructure.repositories.NotaEmpenhoRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotaEmpenhoServiceTest {

    @Mock
    private NotaEmpenhoRepository repository;

    @Mock
    private FornecedorService fornecedorService;

    @Mock
    private ContratoService contratoService;

    @Mock
    private ItemService itemService;

    @InjectMocks
    private NotaEmpenhoService service;

    private NotaEmpenho notaEmpenho;
    private Fornecedor fornecedor;

    @BeforeEach
    void setUp() {
        fornecedor = new Fornecedor();
        fornecedor.setId(1L);
        fornecedor.setRazaoSocial("Fornecedor Teste");

        notaEmpenho = new NotaEmpenho("2024001", LocalDate.now(), LocalDate.now().plusDays(30), 5, fornecedor);
        notaEmpenho.setId(1L);
        notaEmpenho.setValorTotal(new BigDecimal("1000.00"));
    }

    @Test
    void deveCriarNotaEmpenhoComSucesso() {
        // Given
        NotaEmpenhoComItensForm form = new NotaEmpenhoComItensForm(
                "2024001",
                LocalDate.now(),
                LocalDate.now().plusDays(30),
                5,
                1L,
                null,
                List.of(1L)
        );

        when(repository.existsByNumeroAndActive("2024001")).thenReturn(false);
        when(fornecedorService.buscarFornecedorPor(1L)).thenReturn(fornecedor);
        when(itemService.buscarPorId(1L)).thenReturn(new br.ufs.sicad.domain.entidades.Item());
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        NotaEmpenho resultado = service.criarComItens(form);

        // Then
        assertNotNull(resultado);
        assertEquals("2024001", resultado.getNumero());
        verify(repository).save(any(NotaEmpenho.class));
    }

    @Test
    void deveLancarExcecaoAoCriarComNumeroExistente() {
        // Given
        NotaEmpenhoComItensForm form = new NotaEmpenhoComItensForm(
                "2024001",
                LocalDate.now(),
                LocalDate.now().plusDays(30),
                5,
                1L,
                null,
                List.of(1L)
        );

        when(repository.existsByNumeroAndActive("2024001")).thenReturn(true);

        // When & Then
        assertThrows(UniqueResourceViolationException.class, () -> {
            service.criarComItens(form);
        });
    }

    @Test
    void deveBuscarNotaEmpenhoPorId() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));

        // When
        NotaEmpenho resultado = service.buscarPorId(1L);

        // Then
        assertNotNull(resultado);
        assertEquals(1L, resultado.getId());
        assertEquals("2024001", resultado.getNumero());
    }

    @Test
    void deveLancarExcecaoAoBuscarNotaEmpenhoInexistente() {
        // Given
        when(repository.findByIdAndActive(999L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            service.buscarPorId(999L);
        });
    }

    @Test
    void deveRetornarNumeroCorretamente() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));

        // When
        String numero = service.getNumero(1L);

        // Then
        assertEquals("2024001", numero);
    }

    @Test
    void deveAtualizarNumeroCorretamente() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.existsByNumero("2024002")).thenReturn(false);
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.setNumero(1L, "2024002");

        // Then
        verify(repository).save(notaEmpenho);
    }

    @Test
    void deveRetornarValorTotalCorretamente() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));

        // When
        BigDecimal valor = service.getValorTotal(1L);

        // Then
        assertEquals(new BigDecimal("1000.00"), valor);
    }

    @Test
    void deveAtualizarValorTotalCorretamente() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.setValorTotal(1L, new BigDecimal("2000.00"));

        // Then
        verify(repository).save(notaEmpenho);
    }

    @Test
    void deveAnularEmpenhoTotal() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.anularEmpenhoTotal(1L);

        // Then
        verify(repository).save(notaEmpenho);
    }

    @Test
    void deveAnularEmpenhoParcial() {
        // Given
        when(repository.findByIdAndActive(1L)).thenReturn(Optional.of(notaEmpenho));
        when(repository.save(any(NotaEmpenho.class))).thenReturn(notaEmpenho);

        // When
        service.anularEmpenhoTotal(1L, new BigDecimal("500.00"));

        // Then
        verify(repository).save(notaEmpenho);
    }
}
